/*
 * Copyright (C) 2017 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.uniq.uniqpos.data.remote

import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import com.uniq.uniqpos.util.await
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import retrofit2.Call
import timber.log.Timber
import java.util.concurrent.Executors
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

abstract class NetworkBoundResource<ResultType, RequestType> @MainThread
internal constructor(private val shouldFetch: Boolean = true) {

    private val result = MediatorLiveData<Resource<ResultType>>()
    private val executor = Executors.newCachedThreadPool()

    val asLiveData: LiveData<Resource<ResultType>>
        get() = result

    init {
        result.value = Resource.loading(null)
        val dbSource = loadFromDb()
        result.addSource(dbSource) {
            result.removeSource(dbSource)
//            result.value = Resource.success(it)
            if (shouldFetch) {
                fetchFromNetwork(dbSource)
            } else {
                result.addSource(dbSource) { newData -> result.value = Resource.success(newData) }
            }
        }
    }

    private fun fetchFromNetwork(dbSource: LiveData<ResultType>) {
        result.addSource(dbSource) { newData -> result.value = Resource.loading(newData) }
        GlobalScope.launch(Dispatchers.Main) {
            val lastSync = fetchLastSync()
            try {
                createCall(lastSync)?.let { call ->
                    val data = call.await()
                    result.removeSource(dbSource)
                    saveResultAndReInit(data)
                }
            } catch (e: Exception) {
                Timber.i("Fetch from network error... $e")
                onFetchFailed()
                result.removeSource(dbSource)
                result.addSource(dbSource) { newData -> result.postValue(Resource.error(e.message, newData)) }
            }
        }
    }

    @MainThread
    private suspend fun saveResultAndReInit(response: RequestType?) {
        withContext(Dispatchers.IO){
            saveCallResult(response)
        }
        result.addSource(loadFromDb()) { newData ->
            result.value = Resource.success(newData)
        }
    }

    private suspend fun fetchLastSync(): Long {
        return suspendCoroutine { continuation ->
            executor.submit {
                val last = getLastSync()
                continuation.resume(last ?: 0)
            }
        }
    }

    @WorkerThread
    protected abstract fun saveCallResult(item: RequestType?)

    @MainThread
    protected abstract fun loadFromDb(): LiveData<ResultType>

    @MainThread
    protected abstract fun createCall(lastSync: Long): Call<RequestType>?

    @MainThread
    protected abstract fun getLastSync(): Long?

    @MainThread
    protected fun onFetchFailed() {}

}
