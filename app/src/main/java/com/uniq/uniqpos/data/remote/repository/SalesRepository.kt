package com.uniq.uniqpos.data.remote.repository

import androidx.lifecycle.LiveData
import android.os.Build
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.data.local.dao.*
import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.remote.NetworkBoundResource
import com.uniq.uniqpos.data.remote.Resource
import com.uniq.uniqpos.data.remote.model.*
import com.uniq.uniqpos.data.remote.service.ReportService
import com.uniq.uniqpos.data.remote.service.SalesService
import com.uniq.uniqpos.model.Discount
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.model.TaxSales
import com.uniq.uniqpos.util.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import retrofit2.await
import retrofit2.awaitResponse
import timber.log.Timber
import java.util.*
import javax.inject.Inject
import kotlin.collections.ArrayList
import kotlin.collections.HashMap
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * Created by ANNASBlackHat on 22/10/2017.
 */
class SalesRepository @Inject constructor(
    private val salesDao: SalesDao,
    private val reportService: ReportService,
    private val salesService: SalesService,
    private val lastSyncDao: LastSyncDao,
    private val customerDao: CustomerDao,
    private val productDao: ProductDao,
    private val outletDao: OutletDao
) {

    fun getSalesByOpenShift(openShiftFkid: Long) = salesDao.salesHistoryByOpenShift(openShiftFkid)
    suspend fun getSalesByIds(ids: List<String>) = salesDao.salesHistoryByIds(ids)
    fun getPiutangBySalesId(id: String) = salesDao.getPiutangBySalesId(id)
    suspend fun getSalesCartById(id: String) = salesDao.getSalesCartById(id)
    suspend fun getReceiptReceivers() = salesDao.getReceiptReceivers()

    suspend fun saveSales(salesEntity: SalesEntity) {
        Timber.i(">>> SAVE SALES : ${Gson().toJson(salesEntity)}")
        withContext(Dispatchers.IO) {
            salesDao.saveSaleAfterPayment(salesEntity)
            salesEntity.payments.firstOrNull { it.method == "PIUTANG" }?.let { payment ->
                val total = if (payment.pay < 0) payment.pay * -1 else payment.pay
                val piutangEntity = PiutangEntity(
                    piutangId = System.currentTimeMillis(),
                    total = total,
                    unpaid = total,
                    dueDate = payment.dueDate,
                    salesFkid = salesEntity.noNota,
                    info = payment.info ?: "-"
                )
                salesDao.savePiutang(piutangEntity)
            }
            salesEntity.orderList?.filter { it.member != null }?.map { it.member }
                ?.let {
                    customerDao.saveMember(it.filterNotNull())
                }
            updateQtyStock(salesEntity)

//                salesService.postSales(salesEntity).await { ids ->
//                    salesEntity.synced = true
//                    salesDao.updateSales(salesEntity)
//                    piutangEntity?.let { piutang ->
//                        ids?.takeIf { it.piutangId > 0 }?.let {
//                            salesDao.updatePiutangId(piutang.piutangId, ids.piutangId)
//                            salesDao.updatePiutang(piutang.copy(piutangId = ids.piutangId, synced = true))
//                        } ?: kotlin.run { salesDao.deletePiutang(piutang) }
//                    }
//                }
        }
    }

    //update qty stock after sales or refund
    private fun updateQtyStock(sales: SalesEntity) {
        sales.orderList?.forEach { order ->
            var numberHelper = 1
            if ((order.isItemVoid && sales.status?.lowercase() == "success") || (sales.status?.lowercase() == "refund" && !order.isItemVoid)) numberHelper =
                -1
            productDao.minusQtyStockProduct(
                order.qty * numberHelper,
                order.product?.productDetailId.safe()
            )
            order.extra.forEach { orderExtra ->
                productDao.minusQtyStockProduct(
                    orderExtra.qty * numberHelper,
                    orderExtra.product?.productDetailId.safe()
                )
            }
        }
    }

    suspend fun updateCartToPaid(cartId: String) {
        withContext(Dispatchers.IO) {
            salesDao.getTmpSalesById(cartId)?.let { tmpSalesEntity ->
                tmpSalesEntity.timeModified = System.currentTimeMillis()
                tmpSalesEntity.synced = false
                tmpSalesEntity.status = Constant.SALES_STATUS_PAID
                tmpSalesEntity.noNota = ""
                salesDao.updateTmpSale(tmpSalesEntity)
                salesService.postTmpSales(tmpSalesEntity).await {
                    salesDao.updateTmpSale(tmpSalesEntity.copy(synced = true))
                }
            }
        }
    }

    suspend fun directSyncSales(salesEntity: SalesEntity) {
        withContext(Dispatchers.IO) {
            salesDao.getSalesById(salesEntity.noNota)?.let { salesEntity ->
                salesService.postSales(salesEntity).await { ids ->
                    salesDao.updateSales(salesEntity.copy(synced = true))
                }
            }
                ?: kotlin.run { Timber.i("[WARN] no sales with id '${salesEntity.noNota}'") }

            salesDao.getTmpSalesById(salesEntity.noNota)?.let { tmpSalesEntity ->
                val status = salesService.postTmpSales(tmpSalesEntity).await {
                    salesDao.updateTmpSale(tmpSalesEntity.copy(synced = true))
                }
                if (!status) Timber.i("sales cart '${salesEntity.noNota}' failed to send to server")
            }
                ?: kotlin.run { Timber.i("[WARN] no sales cart with id '${salesEntity.noNota}'") }
        }
    }

    fun totalSalesByShift(openShiftId: Long) = salesDao.getTotalSalesByShift(openShiftId)

    fun countTotalClosedShift(timeStart: Long) = salesDao.countCashRecapByTime(timeStart)

    suspend fun countSalesToday(): Int {
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                val result = salesDao.countSalesToday(
                    Utils.getMinTimeMillisToday(),
                    Utils.getMaxTimeMillisToday()
                )
                continuation.resume(result)
            }
        }
    }

    //sales only updated when status changed (from success to refund)
    suspend fun updateSales(salesEntity: SalesEntity, updateStock: Boolean = true) {
        withContext(Dispatchers.IO) {
            try {
                salesDao.updateSales(salesEntity.copy(synced = false))
                salesService.postSales(salesEntity).await {
                    Timber.i("sales ${salesEntity.noNota} synced as ${salesEntity.status}")
                    salesDao.updateSales(salesEntity.copy(synced = true))
                }

                //if this is refund, qty should be updated
                if (updateStock)
                    updateQtyStock(salesEntity)
            } catch (e: Exception) {
                Timber.i("[ERROR] updating sales err: $e")
            }
        }
    }

    suspend fun saveTmpSales(tmpSalesEntity: TmpSalesEntity, isDirectSync: Boolean = true) {
        withContext(Dispatchers.IO) {
            try {
                salesDao.saveTmpSales(tmpSalesEntity)
                Timber.i("cart '${tmpSalesEntity.noNota}' saved")
            } catch (e: Exception) {
                Timber.i("saving cart '${tmpSalesEntity.noNota}' error : $e")
            }
            if (isDirectSync) {
                salesService.postTmpSales(tmpSalesEntity).await {
                    salesDao.updateTmpSale(tmpSalesEntity.copy(synced = true))
                }
            }
        }
    }

    suspend fun updateTmpSale(tmpSalesEntity: TmpSalesEntity, isDirectSync: Boolean = true) {
        withContext(Dispatchers.IO) {
            salesDao.getTmpSalesById(tmpSalesEntity.noNota)?.let {
                tmpSalesEntity.timeModified = System.currentTimeMillis()
                tmpSalesEntity.synced = false
                salesDao.updateTmpSale(tmpSalesEntity)
                if (isDirectSync) {
                    salesService.postTmpSales(tmpSalesEntity).await {
                        salesDao.updateTmpSale(tmpSalesEntity.copy(synced = true))
                    }
                }
            }
                ?: kotlin.run { Timber.i("updating cart '${tmpSalesEntity.noNota}' failed, not exist in db") }
        }
    }

    fun getTransactionCartLive(outletId: Int?): LiveData<List<TmpSalesEntity>> {
        return salesDao.getTransactionCartLive(outletId)
    }

    fun getTrasactionCartCountLive(): LiveData<Int> {
        return salesDao.getTransactionCartCount()
    }

    fun getPendingPrintCountLive(): LiveData<Int> {
        return salesDao.countPendingPrint()
    }

    suspend fun getTmpSalesLocal(outletId: Int?): List<TmpSalesEntity> {
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                val result = salesDao.getTmpSalesLocal(outletId)
                continuation.resume(result)
            }
        }
    }

    fun getOrderSalesLive(outletId: Int): LiveData<Resource<List<OrderSalesEntity>>> {
        return object :
            NetworkBoundResource<List<OrderSalesEntity>, ServerResponseList<OrderSalesEntity>>() {
            override fun saveCallResult(item: ServerResponseList<OrderSalesEntity>?) {
                item?.data?.let {
                    it.forEach { it.synced = true }
                    salesDao.saveOrderSales(it)
                    lastSyncDao.saveLastSync(
                        LastSyncEntity(
                            OrderSalesEntity::class.java.simpleName,
                            item.millis
                        )
                    )
                }
            }

            override fun loadFromDb() = salesDao.getOrderSales()
            override fun createCall(lastSync: Long) = salesService.getOrderSales(outletId, lastSync)
            override fun getLastSync() =
                lastSyncDao.getLastSync(OrderSalesEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    suspend fun updateOrderSales(order: OrderSalesEntity) {
        withContext(Dispatchers.IO) {
            salesDao.updateOrderSales(order)
            salesService.updateOrderSale(order).awaitBase {
                if (it?.status == true) {
                    order.synced = true
                    salesDao.updateOrderSales(order)
                }
            }
        }
    }

    suspend fun updateOrderSalesStatus(
        order: OrderSalesEntity,
        employeeId: Int,
        message: String? = null
    ) {
        withContext(Dispatchers.IO) {
            salesDao.updateOrderSales(order)
            salesService.updateOrderSalesStatus(
                order.orderSalesId,
                order.status,
                employeeId,
                message
            ).awaitBase {
                if (it?.status == true) {
                    order.synced = true
                    salesDao.updateOrderSales(order)
                }
            }
        }
    }

    fun getSalesCart(outletId: Int): LiveData<Resource<List<TmpSalesEntity>>> {
        return object :
            NetworkBoundResource<List<TmpSalesEntity>, ServerResponseList<TmpSalesEntity>>() {
            override fun saveCallResult(item: ServerResponseList<TmpSalesEntity>?) {
                //let sync process do the job, comment below code..
//                item?.data?.let {
//                    it.forEach { it.synced = true }
//                    salesDao.saveTmpSalesList(it)
//                    lastSyncDao.saveLastSync(LastSyncEntity(TmpSalesEntity::class.java.simpleName, item.millis))
//                }
            }

            override fun loadFromDb() = salesDao.getTmpSales(outletId)
            override fun createCall(lastSync: Long) = salesService.getTmpSales(outletId, lastSync)
            override fun getLastSync() =
                lastSyncDao.getLastSync(TmpSalesEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    fun getReservations(outletId: Int?): LiveData<Resource<List<ReservationEntity>>> {
        return object :
            NetworkBoundResource<List<ReservationEntity>, ServerResponseList<ReservationEntity>>() {
            override fun saveCallResult(item: ServerResponseList<ReservationEntity>?) {
                item?.data?.let { items ->
                    items.forEach { it.synced = true }
                    salesDao.saveReservations(items)
                    lastSyncDao.saveLastSync(
                        LastSyncEntity(
                            TmpSalesEntity::class.java.simpleName,
                            item.millis
                        )
                    )
                }
            }

            override fun loadFromDb() = salesDao.getReservationsLive()
            override fun createCall(lastSync: Long) =
                salesService.getReservationByOutlet(outletId, lastSync)

            override fun getLastSync() =
                lastSyncDao.getLastSync(ReservationEntity::class.java.simpleName)?.lastSync

        }.asLiveData
    }

    fun syncSales(outletId: Int? = null, deviceId: String) {
        val salesUnSync = salesDao.getUnsyncedSales()
        Timber.i("${salesUnSync.size} sales unsynced")
        salesUnSync.forEach { sales ->
            Timber.i("push sales: ${sales.noNota}")
            salesService.postSales(sales).await {
                Timber.i("sales ${sales.noNota} synced")
                salesDao.updateSales(sales.copy(synced = true))
            }
        }

        outletId?.let { id ->
            outletDao.getUnsyncedOpenShifts()
                .forEach { openShift ->
                    val map = HashMap<String, Any?>()
                    map["open_shift_id"] = openShift.openShiftId
                    map["outlet_fkid"] = id
                    map["employee_fkid"] = openShift.employeeFkid
                    map["shift_fkid"] = openShift.shiftFkid
                    map["time_open"] = openShift.timeOpen
                    map["early_cash"] = openShift.earlyCash
                    map["device_name"] = "${Build.BRAND} ${Build.MODEL}"
                    map["device_id"] = deviceId
                    salesService.addOpenShift(map).await {
                        outletDao.addOpenShift(openShift.copy(synced = true))
                    }
                }

            salesDao.getUnsyncedCashRecap()
                .forEach { cashRecap ->
                    reportService.postCashRecap(cashRecap)
                        .await { salesDao.saveCashRecap(cashRecap.copy(synced = true)) }
                }

            syncSalesOnly(outletId)

            //get cash recap
            var sync = lastSyncDao.getLastSync(CashRecapEntity::class.java.simpleName)
            reportService.getCashRecap(outletId, sync?.lastSync.safe()).awaitList { cashRecapList ->
                cashRecapList.forEach { cashRecap -> cashRecap.synced = true }
                salesDao.saveCashRecaps(cashRecapList)
                lastSyncDao.saveLastSync(
                    LastSyncEntity(
                        CashRecapEntity::class.java.simpleName,
                        System.currentTimeMillis()
                    )
                )
            }

            //sync order sales online
            syncOrderSales(outletId)

            salesDao.getUnsyncedPiutangHistory().forEach { piutang ->
                salesService.postPiutangHistory(piutang).await { id ->
                    salesDao.updatePiutangHistoryId(
                        id?.piutangHistoryId.safe(),
                        piutang.piutangHistoryId
                    )
                    salesDao.savePiutangHistory(piutang.copy(synced = true))
                }
            }

            sync = lastSyncDao.getLastSync(PiutangEntity::class.java.simpleName)
            salesService.getPiutang(outletId, sync?.lastSync.safe()).awaitListBase { response ->
                response.data?.let { listPiutang ->
                    listPiutang.forEach { it.synced = true }
                    //update piutang id in localdb
                    syncPiutangId(listPiutang)

                    salesDao.savePiutangList(listPiutang)
                    lastSyncDao.saveLastSync(
                        LastSyncEntity(
                            PiutangEntity::class.java.simpleName,
                            response.millis
                        )
                    )
                }
            }

            sync = lastSyncDao.getLastSync(PiutangHistoryEntity::class.java.simpleName)
            salesService.getPiutangHistory(outletId, sync?.lastSync.safe())
                .awaitListBase { response ->
                    response.data?.let { piutangHistory ->
                        piutangHistory.forEach { it.synced = true }
                        salesDao.savePiutangHistoryList(piutangHistory)
                        lastSyncDao.saveLastSync(
                            LastSyncEntity(
                                PiutangHistoryEntity::class.java.simpleName,
                                response.millis
                            )
                        )
                    }
                }
        }

        //post unscync sales refund
        val refunds = salesDao.getUnsyncedRefund()
        refunds.forEach { ref ->
            salesService.postRefund(ref).await {
                salesDao.updateRefund(ref.copy(synced = true))
            }
        }

        syncTempSales(outletId)
        Timber.i("Sync Sales Finish...")
    }

    private fun syncSalesOnly(outletId: Int) {
        //Get sales from server
        val sync = lastSyncDao.getLastSync(SalesEntity::class.java.simpleName)
        var isStillFound: Boolean
        var page = 0
        var lastTimeMillis = sync?.lastSync.safe()
        do {
            var foundSize = 0
            isStillFound =
                salesService.getSalesToday(outletId, page++, lastTimeMillis).awaitListBase {
                    it.data?.let { data ->
                        if (data.isNotEmpty()) {
                            saveSalesToLocal(data)
                            lastTimeMillis = it.millis
                        }
                        Timber.i("sync sales got ${data.size} data, millis: ${it.millis}")
                        if (data.size in 1..5) Timber.i("sales Ids : ${data.map { it.salesId }}")

                        lastSyncDao.saveLastSync(
                            LastSyncEntity(
                                SalesEntity::class.java.simpleName,
                                it.millis
                            )
                        )
                    }
                    foundSize = it.data?.size.safe()
                }
            if (isStillFound) isStillFound = foundSize > 0
        } while (isStillFound)
        Timber.i("sync sales finish, $page pages | lastTimeMillis $lastTimeMillis")
    }

    //call before close shift,
    //this function will make sure total sales are synced with server
    fun validateSales(openShiftId: Long, outletId: Int) {
        //get salesIds
        val salesIds = salesDao.getSalesIdsByShift(openShiftId)
        Timber.i("validate sales, openShiftId: $openShiftId | sales ids: ${Gson().toJson(salesIds)}")
        salesService.validateSalesSyncWithServer(openShiftId, salesIds.joinToString())
            .await { unSync ->
                //if server return any data, those data is what not available on this device
                Timber.i("sales sync validation: $unSync")
                if (unSync?.get("ids_to_pull")?.size.safe() > 0) {
                    salesDao.getOpenShiftById(openShiftId)?.let { openShiftEntity ->
                        lastSyncDao.saveLastSync(
                            LastSyncEntity(
                                SalesEntity::class.java.simpleName,
                                openShiftEntity.timeOpen
                            )
                        )
                        Timber.i("last sync sales reset to: ${openShiftEntity.timeOpen}")
                        syncSalesOnly(outletId)
                    }
                }
            }
    }

    fun syncTempSales(outletId: Int?) {
        // Post unsynced temporary sales to server
        val unSynced = salesDao.getUnsyncedTmpSales()
        Timber.i("Unsynced tmpSales size : ${unSynced.size}")
        unSynced.forEach { tmpSale ->
            Timber.i("Push unsync tmp sales to server... ${tmpSale.noNota}")
            salesService.postTmpSales(tmpSale).await {
                salesDao.updateTmpSale(tmpSale.copy(synced = true))
            }
        }

        outletId?.let { outletId ->
            var sync = lastSyncDao.getLastSync(TmpSalesEntity::class.java.simpleName)
            salesService.getTmpSales(outletId, sync?.lastSync.safe()).awaitListBase { response ->
                response.data?.forEach { tmpSalesRemote ->
                    val tmpSalesLocal = salesDao.getTmpSalesById(tmpSalesRemote.noNota)
                    Timber.i("tmp sales '${tmpSalesRemote.noNota}' last sync local: ${tmpSalesLocal?.timeModified} VS remote: ${tmpSalesRemote.timeModified} | status (remote): ${tmpSalesRemote.status} VS local: ${tmpSalesLocal?.status}")
                    if (tmpSalesLocal != null && tmpSalesLocal.timeModified > tmpSalesRemote.timeModified) {
                        Timber.i("saving temp sales should skip >>")
                    }

                    //only sync if local data is pending, or not been synced to this device
                    //if its paid already, then skip
                    if (tmpSalesLocal?.status == Constant.SALES_STATUS_PENDING || tmpSalesLocal == null) {
                        salesDao.saveTmpSales(tmpSalesRemote.copy(synced = true))
                    } else if (tmpSalesLocal.status == Constant.SALES_STATUS_PAID && tmpSalesRemote.status == Constant.SALES_STATUS_PENDING) {
                        Timber.i("sales is ${tmpSalesLocal.status}... SKIP sync from remote, only set sync to false")
                        //set sync to false, so that in next sync, it will push to server 
                        salesDao.saveTmpSales(tmpSalesLocal.copy(synced = false))
                    } else {
                        salesDao.saveTmpSales(tmpSalesRemote.copy(synced = true))
                    }
                }
                lastSyncDao.saveLastSync(
                    LastSyncEntity(
                        TmpSalesEntity::class.java.simpleName,
                        response.millis
                    )
                )
            }
        }

        //delete useless temporary sales data
        // salesDao.deleteSyncedAndPaidTmpSales()

        //delete tmp sales in the last 24 hours
        val cal = Calendar.getInstance()
        cal.add(Calendar.HOUR, -24)
        salesDao.deleteSyncedAndPaidTmpSalesByTime(cal.getTimeInMillis())
    }

    fun syncOrderSales(outletId: Int) {
        var lastSync = lastSyncDao.getLastSync(OrderSalesEntity::class.java.simpleName)?.lastSync
            ?: 0
        salesService.getOrderSales(outletId, lastSync).awaitListBase { response ->
            response.data?.let { items ->
                items.forEach { it.synced = true }
                salesDao.saveOrderSales(items)
            }
            lastSyncDao.saveLastSync(
                LastSyncEntity(
                    OrderSalesEntity::class.java.simpleName,
                    response.millis
                )
            )
        }

        salesDao.getUnsyncOrderSales().let { items ->
            items.forEach { order ->
                salesService.updateOrderSale(order.copy(items = "")).awaitBase {
                    if (it?.status == true) {
                        order.synced = true
                        salesDao.updateOrderSales(order)
                    }
                }
            }
        }
    }

    private fun saveSalesToLocal(sales: List<Sales>) {
        val salesList = ArrayList<SalesEntity>()
        sales.forEach {
            val oderList = ArrayList<Order>()
            it.detail?.filter { it.parent == null }?.forEach { di ->
                val product = ProductEntity(
                    di.productFkid,
                    productSubcategoryFkid = di.productSubcategoryFkid,
                    productTypeFkid = di.productTypeFkid,
                    name = di.name,
                    photo = di.photo,
                    productCategoryFkid = di.productCategoryFkid,
                    sku = di.sku,
                    dataCreated = di.dataCreated,
                    dataStatus = di.dataStatus,
                    barcode = di.barcode,
                    adminFkid = di.adminFkid,
                    productDetailId = di.productDetailFkId
                )
                val extraList = ArrayList<Order>()
                it.detail.filter { it.parent != null && (it.parent == di.salesDetailId || it.parent == di.salesVoidId) }
                    .forEach { e ->
                        val extra = ProductEntity(
                            e.productFkid,
                            productSubcategoryFkid = e.productSubcategoryFkid,
                            productTypeFkid = e.productTypeFkid,
                            name = e.name,
                            photo = e.photo,
                            productCategoryFkid = e.productCategoryFkid,
                            sku = e.sku,
                            dataCreated = e.dataCreated,
                            dataStatus = e.dataStatus,
                            barcode = e.barcode,
                            adminFkid = e.adminFkid,
                            productDetailId = e.productDetailFkId
                        )
                        extraList.add(
                            Order(
                                extra, e.qty?.toInt() ?: 0, e.price?.toInt()
                                    ?: 0, e.subTotal?.toInt() ?: 0, extraType = e.childType
                            )
                        )
                    }
                val discount = Discount(
                    discountNominal = di.discount.safeToInt(),
                    discountInfo = di.discountInfo
                )
                oderList.add(
                    Order(
                        product,
                        di.qty?.toInt() ?: 0,
                        di.price?.toInt()
                            ?: 0,
                        di.subTotal?.toInt() ?: 0,
                        extra = extraList,
                        isItemVoid = di.isItemVoid,
                        discount = discount,
                        note = di.note,
                        promotion = di.promotions?.firstOrNull()
                    )
                )
            }
            val taxList = ArrayList<TaxSales>()
            it.tax?.forEach { t ->
                val tax = TaxSales(
                    t.salesTaxId, t.name, t.total ?: 0, t.taxType, t.jumlah
                        ?: 0, t.taxCategory
                )
                taxList.add(tax)
            }

            val discount = Discount(
                it.discount ?: 0, it.discountInfo, it.voucher
                    ?: 0, it.voucherInfo, discountNominal = it.discount
                    ?: 0, voucherNominal = it.voucher ?: 0
            )
            val sales = SalesEntity(
                it.salesId,
                oderList,
                it.grandTotal
                    ?: 0,
                it.customerName,
                it.qtyCustomers ?: 0,
                it.timePrediction ?: 0,
                taxList,
                it.payment
                    ?: "",
                status = it.status,
                employeeID = it.employeeFkid,
                outletID = it.outletFkid,
                outletName = it.outletName,
                timeCreated = it.timeCreated
                    ?: 0,
                timeModified = it.timeModified
                    ?: 0,
                openShiftId = it.openShiftFkid,
                synced = true,
                discount = discount,
                displayNota = it.displayNota
                    ?: "",
                table = it.diningTable.safe(),
                refundReason = it.reason,
                receiptReceiver = it.receiptReceiver,
                salesTag = it.salesTag,
                salesRefund = it.salesRefund
            )
            it.payments?.let { p -> sales.payments = p }
            it.promotion?.let { p -> sales.promotions = p }

            salesList.add(sales)
        }
        salesDao.saveSales(salesList)
    }

    //synchronize piutang id according to id got from server
    //piutangList : list piutang got from server
    private fun syncPiutangId(piutangList: List<PiutangEntity>) {
        // Process in batches of 500 to stay well under SQLite's variable limit
        piutangList.map { it.salesFkid }
            .chunked(500)
            .forEach { salesIdsBatch ->
                salesDao.getPiutangBySalesIds(salesIdsBatch).forEach { piutangLocal ->
                    piutangList.firstOrNull { it.salesFkid == piutangLocal.salesFkid }
                        ?.let { piutangServer ->
                            salesDao.updatePiutangId(
                                piutangLocal.piutangId,
                                piutangServer.piutangId
                            )
                            salesDao.updatePiutangIdOnHistory(
                                piutangLocal.piutangId,
                                piutangServer.piutangId
                            )
                            Timber.d("update piutang id ${piutangLocal.piutangId} to ${piutangServer.piutangId}")
                        }
                }
            }
    }

    suspend fun getSalesRecap(vararg openShiftId: Long): List<SalesEntity> {
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                val data = salesDao.getSalesRecap(*openShiftId)
                continuation.resume(data)
            }
        }
    }

    fun getSalesRecapInBackground(vararg openShiftId: Long): List<SalesEntity> {
        return salesDao.getSalesRecap(*openShiftId)
    }

    fun getCashRecap(): LiveData<Resource<List<CashRecapEntity>>> {
        return object : NetworkBoundResource<List<CashRecapEntity>, Any>(false) {
            override fun saveCallResult(item: Any?) {}
            override fun loadFromDb() = salesDao.getCashRecap()
            override fun createCall(lastSync: Long): Nothing? = null
            override fun getLastSync() = 0L
        }.asLiveData
    }

    /*fun saveCashRecap(cashRecapEntity: CashRecapEntity, offlineMode: Boolean): Observable<Boolean> {
        return Observable.create { e ->
            var status = reportService.postCashRecap(cashRecapEntity).await {
                salesDao.saveCashRecap(cashRecapEntity)
            }
            if (!status && offlineMode) {
                salesDao.saveCashRecap(cashRecapEntity.copy(synced = false))
                status = true
            }
            e.onNext(status)
        }
    }*/

    suspend fun saveCloseShift(cashRecapEntity: CashRecapEntity, offlineMode: Boolean): Boolean {
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                var status = reportService.postCashRecap(cashRecapEntity).awaitBase {
                    salesDao.saveCashRecap(cashRecapEntity)
                }
                continuation.resume(status)
            }
        }
    }

    suspend fun getOperationalCost(vararg openShiftIds: Long): ArrayList<OperationalCost> {
        return suspendCoroutine { continuation ->
            val list = ArrayList<OperationalCost>()
            BACKGROUND.submit {
                openShiftIds.forEach { openShiftId ->
                    reportService.getOperationalCost(openShiftId).awaitList {
                        list.addAll(it)
                    }
                }
                continuation.resume(list)
            }
        }
    }

    suspend fun getOpenShift(vararg openShiftIds: Long): ArrayList<OpenShiftEntity> {
        return suspendCoroutine { continuation ->
            val list = ArrayList<OpenShiftEntity>()
            BACKGROUND.submit {
                openShiftIds.forEach { openShiftId ->
                    salesDao.getOpenShiftById(openShiftId)?.let { list.add(it) }
                }
                continuation.resume(list)
            }
        }
    }

    suspend fun getTotalPiutangPayment(startTime: Long, endTime: Long): Int {
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                val data = salesDao.getTotalPiutangPayment(startTime, endTime)
                continuation.resume(0)
            }
        }
    }

    fun saveRefund(refundEntity: RefundEntity) {
        BACKGROUND.submit {
            salesDao.saveRefund(refundEntity)
            salesService.postRefund(refundEntity).await {
                refundEntity.synced = true
                salesDao.updateRefund(refundEntity)
            }
        }
    }

    suspend fun saveReservation(reservationEntity: ReservationEntity) {
        withContext(Dispatchers.IO) {
            salesDao.saveReservation(reservationEntity)
            salesService.postReservation(reservationEntity).await {
                reservationEntity.synced = true
                salesDao.updateReservation(reservationEntity)
            }
        }
    }

    fun getAvailableShiftOld(outletId: Int, timeOffset: Int) =
        salesService.getAvailableShift(outletId, timeOffset)

    fun getActiveShift(outletId: Int) = salesService.getActiveShift(outletId)

    suspend fun createOpenShift(
        shift: HashMap<String, Any?>,
        offlineMode: Boolean? = false
    ): ServerResponse<OpenShiftEntity> {
        return salesService.addOpenShift(shift).await()
    }

    fun getActiveDevice(openShiftId: Long, deviceId: String) =
        salesService.getActiveDeviceCall(openShiftId, deviceId)

    suspend fun savePendingPrint(pendingPrintEntity: List<PendingPrintEntity>) {
        withContext(Dispatchers.IO) {
            salesDao.savePendingPrint(pendingPrintEntity)
        }
    }

    suspend fun getPendingPrint() = salesDao.getPendingPrint()

    suspend fun deletePendingPrint(pendingPrintEntity: PendingPrintEntity) {
        withContext(Dispatchers.IO) {
            salesDao.deletePendingPrint(pendingPrintEntity)
        }
    }

    suspend fun getPiutangLiveDb(outletId: Int): LiveData<List<PiutangEntity>>{
        Timber.d("load piutang from db...")
        withContext(Dispatchers.IO) {
            Timber.d("load piutang from service...")
            val lastSync =  lastSyncDao.getLastSync(PiutangEntity::class.java.simpleName)?.lastSync.safe()
            val result = salesService.getPiutang(outletId, lastSync).await()
            result.data?.let {data ->
                data.forEach { it.synced = true }
                salesDao.savePiutangList(data)
                lastSyncDao.saveLastSync(
                    LastSyncEntity(
                        PiutangEntity::class.java.simpleName,
                        result.millis
                    )
                )
            }
        }
        Timber.d("return piutang from db...")
        return salesDao.getPiutangLive()
    }

    fun getPiutangLive(outletId: Int?): LiveData<Resource<List<PiutangEntity>>> {
        return object :
            NetworkBoundResource<List<PiutangEntity>, ServerResponseList<PiutangEntity>>() {
            override fun saveCallResult(item: ServerResponseList<PiutangEntity>?) {
                Timber.i("get piutang from api got ${item?.data?.size}")
                item?.data?.let { data ->
                    data.forEach { it.synced = true }
                    syncPiutangId(data)
                    salesDao.savePiutangList(data)
                    lastSyncDao.saveLastSync(
                        LastSyncEntity(
                            PiutangEntity::class.java.simpleName,
                            item.millis
                        )
                    )
                }
            }

            override fun loadFromDb() = salesDao.getPiutangLive()
            override fun createCall(lastSync: Long) = salesService.getPiutang(outletId, lastSync)
            override fun getLastSync() =
                lastSyncDao.getLastSync(PiutangEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    suspend fun savePiutangPayment(piutangHistoryEntity: PiutangHistoryEntity) {
        withContext(Dispatchers.IO) {
            salesDao.updateUnpaidPiutang(piutangHistoryEntity.pay, piutangHistoryEntity.piutangFkid)
            val id = salesDao.savePiutangHistory(piutangHistoryEntity)
            salesService.postPiutangHistory(piutangHistoryEntity).await { ids ->
                ids?.let {
                    salesDao.updatePiutangHistoryId(it.piutangHistoryId, id)
                    salesDao.updatePiutangHistory(
                        piutangHistoryEntity.copy(
                            piutangHistoryId = it.piutangHistoryId,
                            synced = true
                        )
                    )
                }
            }
        }
    }

    suspend fun checkMember(barcode: String, outletId: Int?): ServerResponse<Member> {
        return salesService.checkMember(outletId, barcode).await()
    }

    suspend fun checkSecretCode(secretCode: String): ServerResponse<Member> {
        return salesService.checkSecretCode(secretCode).await()
    }

    suspend fun checkPromotionCode(barcode: String, outletId: Int?): ServerResponse<Promotion> {
        return salesService.checkPromotionCode(outletId, barcode).await()
    }

    suspend fun voidPromotion(barcode: String?, outletId: Int?): ServerResponse<Any> {
        Timber.i("voidPromotion : $barcode")
        return salesService.voidVoucher(outletId, barcode).await()
    }

    suspend fun getOpenShiftHistory() = salesDao.getOpenShift()
    fun getPiutangHistoryById(piutangId: Long) = salesDao.getPiutangHistoryListById(piutangId)
    fun getAvailableDevice(outletId: Int?) = salesService.getAvailableDevice(outletId)
    fun getEditableStatus(salesId: String) = salesService.getSalesCartEditableStatus(salesId)
    fun getSelfOrder(orderCode: String) = salesService.getSelfOrder(orderCode)
    fun countPendingOrder() = salesDao.countPendingOrder()
    fun getSalesNotes() = salesService.getSalesNotes()
    suspend fun fetchCustomerNames(): List<String> = salesDao.fetchCustomerNames()
    suspend fun fetchDiscAndVoucherInfo() = salesDao.fetchDiscountAndVoucherInfo()

    fun processPendingRemovedVoucher(sharedPref: SharedPref, outletId: Int?) {
        try {
            val type = object : TypeToken<ArrayList<String>>() {}.type
            val dataJson = sharedPref.getString(SharedPref.PENDING_VOUCHER_REMOVE)
            if (dataJson.isNullOrBlank()) {
                return
            }
            val pendingList = Gson().fromJson<ArrayList<String>>(dataJson, type)
            val removed = ArrayList<String>()
            Timber.i("total pending voucher to remove: ${pendingList.size}")
            pendingList.forEach { code ->
                salesService.voidVoucher(outletId, code).awaitBase {
//                    pendingList.remove(code)
                    removed.add(code)
                }
            }

            pendingList.removeAll(removed.toSet())
            Timber.i("total pending voucher now: ${pendingList.size}")
            sharedPref.saveJson(SharedPref.PENDING_VOUCHER_REMOVE, pendingList)
        } catch (e: Exception) {
            Timber.i("save or get pending voucher err: $e")
        }
    }

    fun clearOldSales() {
        try {
            val cal = Calendar.getInstance()
            cal.add(Calendar.MONTH, -1)
            Timber.i("clearing old data sales, max: ${cal.timeInMillis} (${cal.time})")
//            Timber.i("oldest sales: ${salesDao.oldestSales()}")

            salesDao.clearOldSales(cal.timeInMillis)
            salesDao.clearOldOpenShift(cal.timeInMillis)
            Timber.i("oldest sales now: ${salesDao.oldestSales()}")
        } catch (e: Exception) {
            Timber.i("clearOldSales err: $e")
        }
    }

    suspend fun getLastSync(): Long? {
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                val data = lastSyncDao.getLastSync(SalesEntity::class.java.simpleName)?.lastSync
                continuation.resume(data)
            }
        }
    }

    fun getSalesCart(id: String) = salesDao.getTmpSalesById(id)

    fun createInstantPayment(salesId: String) = salesService.createInstantPayment(salesId)
    fun checkPaymentStatus(salesId: String) = salesService.checkPaymentStatus(salesId)

    fun getSalesByIds(salesIds: String) = salesService.getSalesByIds(salesIds)
    fun getSingleSales(salesId: String) = salesDao.getSalesById(salesId)
    fun getSalesByIdLive(salesId: String) = salesDao.getSalesByIdLive(salesId)

    fun getSalesTagLive() = salesDao.getSalesTagLive()
    suspend fun fetchSalesTag(outletId: Int): kotlin.collections.List<SalesTagEntity> {
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                salesService.getSalesTag(outletId).awaitList { data ->
                    salesDao.addSalesTag(data)
                    continuation.resume(data)
                }
            }
        }
    }

    suspend fun getDebtPayment(startDate: Long, endDate: Long): List<PiutangHistoryEntity> {
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                val data = salesDao.getPiutangHistory(startDate, endDate)
                continuation.resume(data)
            }
        }
    }

    suspend fun updateOpenShift(openShiftId: Long, timeClose: Long) {
        BACKGROUND.submit {
            salesDao.updateOpenShift(openShiftId, timeClose)
        }
    }
}