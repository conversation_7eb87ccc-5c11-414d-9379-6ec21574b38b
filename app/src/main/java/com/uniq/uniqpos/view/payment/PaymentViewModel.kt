package com.uniq.uniqpos.view.payment

import android.graphics.Bitmap
import android.widget.Toast
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.remote.model.*
import com.uniq.uniqpos.data.remote.repository.OutletRepository
import com.uniq.uniqpos.data.remote.repository.ProductRepository
import com.uniq.uniqpos.data.remote.repository.SalesRepository
import com.uniq.uniqpos.data.remote.repository.SettingRepository
import com.uniq.uniqpos.model.*
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.BaseViewModel
import com.uniq.uniqpos.util.lifecycle.Event
import com.uniq.uniqpos.util.lifecycle.SingleLiveAction
import com.uniq.uniqpos.util.lifecycle.SingleLiveEvent
import com.uniq.uniqpos.util.receipt.NotaManager
import com.uniq.uniqpos.util.receipt.PrintNotaUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import retrofit2.await
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import kotlin.collections.ArrayList
import kotlin.math.abs

/**
 * Created by annasblackhat on 07/12/18
 */

class PaymentViewModel @Inject constructor(
    private val salesRepository: SalesRepository,
    private val productRepository: ProductRepository,
    private val outletRepository: OutletRepository,
    private var settingRepository: SettingRepository,
    private val sharedPref: SharedPref
) : BaseViewModel() {

    var changeTotal = MutableLiveData("0")
    var receiptReceiver = MutableLiveData<String>()
    val cardInformation = MutableLiveData<String>()
    val paymentInformation = MutableLiveData<String>()

    val finishTaskCommand = SingleLiveAction()

    //    val dialogTaskCommand = SingleLiveEvent<String>()
    val cardDialogTaskCommand = SingleLiveAction()
    val pDialogTaskCommand = SingleLiveEvent<Boolean>()
    val paymentSavedTask = SingleLiveEvent<SalesEntity>()
    val printTask = SingleLiveEvent<PrintTask>()
    val openCashDrawerTask = SingleLiveEvent<List<PrinterEntity>>()
    val receiptListRefresh = SingleLiveAction()
    val taskRefreshPaymentList = SingleLiveEvent<Int>()
    val taskRefreshPaymentOption = SingleLiveAction()
    val taskShowCardDialog = SingleLiveAction()
    val taskUseCashDialog = SingleLiveAction()

    val navigateToPaymentQr = SingleLiveEvent<SalesPaymentCreate>()
    val taskPaymentPaid = SingleLiveAction()
    val taskPaymentDetailDialog = SingleLiveEvent<Boolean>() //true: open, false: close

    lateinit var sales: SalesEntity
    val paymentValue = mutableMapOf<Int, Int>()
//    var selectedBank = -1
    val bankList = ArrayList<BankEntity>()
    var salesOriginal: SalesEntity? = null
    val mergeIds = ArrayList<String>()
    val receiptReceiverList = ArrayList<String>()
    val paymentOpion = ArrayList<Int>()

    var piutang: PiutangEntity? = null
    val paymentStatus = mutableMapOf<Int, Boolean>()
    var paymentList = arrayListOf(
        "CASH",
        "CARD",
        "COMPLIMENT",
        "PIUTANG",
        "DUTY MEALS",
        PaymentType.INSTANT_PAYMENT.toString()
    )
    var isOpeningCashDrawer = false
    val forbiddenMultiPayment = arrayListOf("COMPLIMENT", "DUTY MEALS")
    var selectedPaymentIdx = 0
    var selectedBank = -1
    var isPiutang = false
    val paymentListSelected = ArrayList<PaymentSelected>()
    val selectedPayment: PaymentSelected get() = paymentListSelected[selectedPaymentIdx]
    var isInstantPaymentEnable = false //true if user has setup payment gateway

    var role: RoleMobile? = null
    var isTabletMode = false

    val outletData: Outlet? by lazy { sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java) }

    enum class PaymentType(private val originalValue: String) {
        CASH("CASH"), CARD("CARD"), COMPLIMENT("COMPLIMENT"), PIUTANG("PIUTANG"), DUTY_MEALS("DUTY MEALS"), INSTANT_PAYMENT(
            "INSTANT PAYMENT"
        );

        override fun toString(): String {
            return originalValue
        }
    }

    fun initPaymentList() {
        outletData?.let { outlet ->
            val paymentListMap = mapOf(
                "CASH" to outlet.paymentCash,
                "CARD" to outlet.paymentCard,
                "COMPLIMENT" to outlet.paymentCompliment,
                "PIUTANG" to outlet.paymentPiutang,
                "DUTY MEALS" to outlet.paymentDuty,
            )
            for ((pay, enable) in paymentListMap) {
                if (enable == 0) paymentList.remove(pay)
            }

            isInstantPaymentEnable = outlet.paymentInstant == 1
        }
        paymentListSelected.clear()
        paymentList.forEach { pay ->
            paymentListSelected.add(PaymentSelected(pay))
        }
    }

    //this will add CARD payment options, for multi card payment
    fun addPaymentCardOption(): Int {
        //check if card payment is enabled
        val isCardEnabled = outletData?.paymentCard == 1
        if (!isCardEnabled){
            Timber.i("can not enable card option, its disabled")
            return 0
        }

        val idx = paymentListSelected.indexOfLast { it.method == PaymentType.CARD.toString() } + 1
        paymentListSelected.add(idx, PaymentSelected(PaymentType.CARD.toString()))
        Timber.i("payment card added, at $idx, list ${Gson().toJson(paymentListSelected)}")
        return idx
    }

    fun validatePaymentChange(position: Int): Boolean {
        Timber.i("select payment: ${paymentListSelected[position].method} at $position")
        val role = getRoleMobile()
        val hasPermission = when (paymentListSelected[position].method.lowercase()) {
            "compliment" -> role.compliment
            "duty meals" -> role.duty
            else -> true
        }
        if (!hasPermission) {
            _toastMessage.postValue(Event(ToastMessage(messageResId = R.string.no_permission)))
            return false
        }

        //if changed from non-forbidden multiple, to forbidden, e.g from CARD -> Compliment
        if(forbiddenMultiPayment.any { it == paymentListSelected[position].method } && paymentListSelected.any { it.total > 0 }){
            _toastMessage.postValue(
                Event(
                    ToastMessage(
                        "Pembayaran ${forbiddenMultiPayment.joinToString { it }} tidak dapat digabungkan dengan metode pembayaran lainnya",
                        type = Level.ERROR,
                        duration = Toast.LENGTH_LONG
                    )
                )
            )
            return false
        }

        //if user has forbid multi payment, prevent to add
        if(paymentListSelected.any { it.total > 0 && forbiddenMultiPayment.any { forbid -> forbid == it.method } }){
            _toastMessage.postValue(
                Event(
                    ToastMessage(
                        "Pembayaran ${forbiddenMultiPayment.joinToString { it }} tidak dapat digabungkan dengan metode pembayaran lainnya",
                        type = Level.ERROR,
                        duration = Toast.LENGTH_LONG
                    )
                )
            )
            return false
        }

        //invalid if card is chosen but information not provided
        Timber.d("#changepayment pay, ${selectedPayment.method} : ${selectedPayment.total}")
        if (selectedPayment.method == "CARD" && selectedPayment.total > 0 && position != selectedPaymentIdx && cardInformation.value.safe()
                .isEmpty()
        ) {
            _dialogMsg.postValue(Event("Please complete card information!"))
            return false
        }

        //reset card info if user set total to 0
        if (selectedPayment.method == "CARD" && selectedPayment.total == 0) {
            selectedPayment.description = ""
        }

        if (listOf("COMPLIMENT", "DUTY MEALS", "PIUTANG").any { it == selectedPayment.method }) {
            selectedPayment.description = paymentInformation.value.safe()
        } else if (selectedPayment.method == "CARD" && selectedPayment.selectedBankIdx >= 0) {
            selectedPayment.description = bankList[selectedPayment.selectedBankIdx].name.safe()
            selectedPayment.bankInformation = cardInformation.value
        }

        selectedPaymentIdx = position

        //reset
        paymentInformation.postValue(selectedPayment.description)

        refreshPaymentOption()
        return true
    }

    fun validateSplitBill(): Boolean {
        if (sales.orderList?.size.safe() == 1 && sales.orderList?.get(0)?.qty.safe() <= 1) {
            _toastMessage.postValue(
                Event(
                    ToastMessage(
                        type = Level.ERROR,
                        duration = Toast.LENGTH_LONG,
                        messageResId = R.string.qty_impossible_split_bill
                    )
                )
            )
        } else if (sales.orderList?.any { it.promotion != null }.safe()) {
            _toastMessage.postValue(
                Event(
                    ToastMessage(
                        type = Level.ERROR,
                        duration = Toast.LENGTH_LONG,
                        message = "transaction with promotion can not be split!"
                    )
                )
            )
        } else if (mergeIds.isNotEmpty()) {
            _toastMessage.postValue(
                Event(
                    ToastMessage(
                        type = Level.ERROR,
                        duration = Toast.LENGTH_LONG,
                        message = "please go back to save the merged bill!"
                    )
                )
            )
        } else {
            return true
        }
        return false
    }

    fun countChange() {
        val change = paymentListSelected.sumOf { it.total } - getGrandTotal()
        Timber.d("#Payment, change: $change")
//        changeTotal.postValue(change.toString())
        changeTotal.value = change.toCurrency()
    }

    fun refreshPaymentOption() {
        val change = getGrandTotal() - paymentListSelected.sumOf { it.total }
        if (change > 0) {
            paymentOpion.clear()
            paymentOpion.addAll(generatePaymentOptions(change))
            taskRefreshPaymentOption.call()
        }
    }

    private fun getRoleMobile(): RoleMobile {
        return role ?: kotlin.run {
            role = sharedPref.role()
            role!!
        }
    }

    fun getBankMedia(outletid: Int?) = productRepository.getBank(outletid)

    fun loadBankMedia() {
        viewModelScope.launch {
            val bank = await { outletRepository.getMediaBank() }
            bankList.clear()
            bankList.addAll(bank.sortedBy { it.name })
            Timber.d("#bank: $bank")
        }
    }

    fun savePendingPrint(pendingPrintEntity: List<PendingPrintEntity>) {
        viewModelScope.launch {
            salesRepository.savePendingPrint(pendingPrintEntity)
        }
    }

    fun getGrandTotal(): Int {
        return if (piutang != null) piutang?.total.safe() else sales.grandTotal
    }

    fun savePiutang(employeeId: Int?) {
        val total = paymentValue.filter { p ->
            paymentStatus.filter { it.value }.any { it.key == p.key }
        }.values.sum()

        if (total == 0) {
            _dialogMsg.postValue(Event("please input your payment!"))
            return
        }
        if (total > piutang?.unpaid.safe()) {
            _dialogMsg.postValue(Event("total payment exceed limit!"))
            return
        }

        viewModelScope.launch(Dispatchers.IO) {
            val originalSales = salesRepository.getSingleSales(piutang?.salesFkid.safe())
            val salesPayment = originalSales?.payments
            paymentStatus.filter { it.value }.forEach {
                val method = paymentList[it.key].uppercase()
                val payment = PiutangHistoryEntity(
                    total = paymentValue[it.key].safe(),
                    employeeFkid = employeeId ?: 0,
                    method = method,
                    piutangFkid = piutang?.piutangId ?: 0,
                    pay = paymentValue[it.key].safe(),
                    salesFkid = piutang?.salesFkid ?: ""
                )
                salesRepository.savePiutangPayment(payment)
                salesPayment?.apply { add(Payment(method, paymentValue[it.key].safe())) }
            }

            //update sales payment (salesEntity)
            originalSales?.let { salesRepository.updateSales(it, updateStock = false) }
            finishTaskCommand.call()
        }
    }

    fun isForbidMultiPayment(): Boolean {
        return paymentStatus.any { it.value && (paymentList[it.key].lowercase() == "duty meals" || paymentList[it.key].lowercase() == "compliment") }
    }

    fun getSelectedPaymentMethod(): List<String> {
        return paymentStatus.filter { it.value }.map { paymentList[it.key] }
    }

    private fun setPaymentStatus() {
        paymentListSelected.forEachIndexed { index, paymentSelected ->
            paymentStatus[index] = paymentSelected.total > 0
            paymentValue[index] = paymentSelected.total
        }
    }

    fun finishPayment(isForceCash: Boolean = false) {
        Timber.i("#bank cardInfo: ${cardInformation.value}")
        if (selectedPayment.method == PaymentType.CARD.toString()) {
//            bankList[selectedBank].accountNumber = cardInformation.value
            selectedPayment.bankInformation = cardInformation.value
        }
        if (selectedPayment.method == PaymentType.CARD.toString() && cardInformation.value.isNullOrBlank()){
            _toastMessage.postValue(Event(ToastMessage("please complete card information", type = Level.WARNING)))
            taskPaymentDetailDialog.postValue(true)
            return
        }


        //show confirmation to use cash
        if(!isForceCash && paymentList.any { it == "CASH" }  && paymentListSelected.sumOf { it.total } < getGrandTotal()){
            taskUseCashDialog.call()
            return
        }

        //if no payment selected, use cash
        if(isForceCash || paymentListSelected.none { it.total > 0 }){
            selectedPayment.total = getGrandTotal()
            selectedPayment.method = PaymentType.CASH.toString()
        }

        setPaymentStatus()
        paymentInformation.value?.let { selectedPayment.description = it }

        val complimentInfo =
            paymentListSelected.firstOrNull { it.method == "COMPLIMENT" }?.description.safe()
        val dueDate =
            paymentListSelected.firstOrNull { it.method == "PIUTANG" }?.description.safe()
        val employee =
            sharedPref.getJson(SharedPref.EMPLOYEE_DATA, Employee::class.java) ?: Employee()
        val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java) ?: Outlet()
        val shift = sharedPref.getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java) ?: ShiftOpen()

        savePayment(
            complimentInfo,
            dueDate,
            getGrandTotal(),
            receiptReceiver.value.safe(),
            employee,
            shift,
            outlet
        )
    }

    fun savePayment(
        info: String,
        dueDate: String,
        grandTotal: Int = 0,
        receiptReceiver: String,
        employee: Employee,
        shift: ShiftOpen,
        outlet: Outlet
    ) {
        val totalPay = paymentListSelected.filter { p -> p.method != PaymentType.CASH.toString() }.sumOf { it.total }
        var paymentString = paymentListSelected.filter { it.total > 0 }.joinToString { it.method }
        sales.payments.clear()

        if (info.length > 150) {
            _dialogMsg.postValue(Event("Compliment information is too long!"))
            return
        }

        paymentListSelected.filter { it.total > 0 }.forEach { payment ->
            val total =
                if (payment.method == PaymentType.CASH.toString()) grandTotal - totalPay else payment.total

            if (payment.method != PaymentType.CASH.toString() && total > sales.grandTotal) {
                _dialogMsg.postValue(Event("Pembayaran ${payment.method} yang anda masukan melebihi batas!"))
                return
            }

            //validate card information
            if (payment.method == PaymentType.CARD.toString() && payment.bankInformation.isNullOrBlank()) {
                _dialogMsg.postValue(Event("Mohon masukan informasi kartu!"))
                return
            }

            val dueDateL: Long = dueDate.toDate()?.time.safe()
            val pay =
                if (payment.method.toLower() == "piutang") payment.total * -1 else payment.total
            sales.payments.add(Payment(payment.method, total,
                bankList.getSafe(payment.selectedBankIdx)?.copy(accountNumber = payment.bankInformation),
                payment.bankInformation, pay, dueDateL))
        }

        val isContainForbiddenMultiple = paymentListSelected.any {
            it.total > 0 && listOf(
                "DUTY MEALS",
                "COMPLIMENT"
            ).any { forbid -> forbid == it.method }
        }
        if (isContainForbiddenMultiple && sales.payments.size > 1) {
            _dialogMsg.postValue(Event("Terdapat pembayaran yang tidak mendukung berbagai macam pembayaran sekaligus!"))
            return
        }

        if (shift.openShiftId == 0L){
            _dialogMsg.postValue(Event("Tidak dapat menentukan Shift yang berlaku! silahkan restart aplikasi"))
            return
        }

        if (System.currentTimeMillis() < shift.timeOpen) {
            _dialogMsg.postValue(Event("Tanggal/Jam pada device anda sepertinya belum sesuai. Harap sesuaikan terlebih dahulu!"))
            return
        }

        if (receiptReceiver.isNotEmpty()) {
            if (receiptReceiver.isNumeric() && !isValidPhoneNumber(receiptReceiver)) {
                _dialogMsg.postValue(Event("Phone Number is not valid!"))
                return
            } else if (!receiptReceiver.isNumeric() && !receiptReceiver.isValidEmail()) {
                _dialogMsg.postValue(Event("Email address is not valid!"))
                return
            }
            sales.receiptReceiver = receiptReceiver
        }

        if (sales.payments.isEmpty() && !paymentList.any { it == "CASH" }) {
            _dialogMsg.postValue(Event("Please choose payment method!"))
            return
        }

        if(sales.payments.sumOf { it.total } != getGrandTotal()) {
            _dialogMsg.postValue(Event("total payment mismatch with grand total,\npayment is ${sales.payments.sumOf { it.total } } while it should ${getGrandTotal()}"))
            return
        }

        //finish validation, start progress dialog bar
        pDialogTaskCommand.postValue(true)

        Timber.i(">> Payment String : $paymentString")
        paymentString = if (sales.payments.isEmpty()) {
            sales.payments.add(Payment("CASH", sales.grandTotal))
            "CASH"
        } else paymentString

        sales.employeeID = employee.employeeId
        sales.employeeName = employee.name
        sales.payment = paymentString
        sales.openShiftId = shift.openShiftId
        sales.timeCreated = System.currentTimeMillis()

        viewModelScope.launch {
            val printers = settingRepository.getPrinterList()
            if (salesOriginal == null) {
                val offPrinters = sharedPref.deactivatePrinterList()
                val printerReceipts = printers.filter { p -> p.settingPrintreceipt == "1" }
                Timber.i("deactivate printer: $offPrinters | printer receipts: ${printerReceipts.map { it.address }}")
                printerReceipts.filter { p -> !offPrinters.any { addrs -> addrs == p.address } }
                    .takeIf { it.isNotEmpty() }?.let { printerReceipt ->
                        openCashDrawerTask.postValue(printerReceipt)
                    }
            }

            val salesCount = salesRepository.countSalesToday()

            sales.displayNota = generateDisplayNota(outlet.adminFkid, outlet.outletId, salesCount)
            val originalNoNota = sales.noNota
            salesOriginal?.orderList?.takeIf { it.isNotEmpty() }?.let {
                sales.noNota = Utils.generateNoNota()
                Timber.i("Generated New No Nota... ")
            }
            Timber.i("Display Nota : ${sales.displayNota} | No Nota : ${sales.noNota}")

            salesRepository.saveSales(sales)
            Timber.i("Sales with id : '${sales.noNota}' saved. Payment ==> ${Gson().toJson(sales.payments)} | paymentList ${Gson().toJson(paymentListSelected)}")

            salesOriginal?.orderList?.takeIf { it.isNotEmpty() }?.let {
                salesRepository.updateTmpSale(
                    TmpSalesEntity(
                        originalNoNota,
                        sales = Gson().toJson(salesOriginal),
                        outletId = outlet.outletId!!
                    ), false
                )
                Timber.i("update tmp sales '$originalNoNota' to new grandTotal ${salesOriginal?.grandTotal} | total qty: ${salesOriginal?.orderList?.sumBy { it.qty }}")
            } ?: kotlin.run {
                salesRepository.updateTmpSale(
                    TmpSalesEntity(
                        sales.noNota,
                        status = Constant.SALES_STATUS_PAID,
                        outletId = outlet.outletId!!
                    ), false
                )
                Timber.i("update tmp sales '${sales.noNota}' to status ${Constant.SALES_STATUS_PAID}")
            }

            mergeIds.forEach {
                salesRepository.updateTmpSale(
                    TmpSalesEntity(
                        it, status = Constant.SALES_STATUS_PAID, outletId = outlet.outletId!!
                    ), false
                )
            }
            Timber.i("updated cart merge id : $mergeIds")

            if (sales.table.isNotEmpty()) {
                outletRepository.updateTableStatus(
                    DiningTableEntity(
                        tableName = sales.table, status = Constant.TABLE_STATUS_USED
                    )
                )
                Timber.i("Table status updated!")
            }

            //remove voucher from shared pref if needed
            sales.promotions?.firstOrNull()?.let { promoApplied ->
                sharedPref.getJson(SharedPref.TEMP_VOUCHER, Promotion::class.java)
                    ?.let { promotion ->
                        if (promotion.code == promoApplied.code) {
                            sharedPref.clearJson(SharedPref.TEMP_VOUCHER)
                        }
                    }
            }

            salesOriginal?.let {
                Timber.i(">>> [split bill] Print Nota!")
                Timber.i("printer size : ${printers.size}")
                val ticketList = settingRepository.getPrinterTicketOrders()
                val printData = NotaManager.createNota(
                    printers, ticketList, sales, outlet, employee, isOpenCashDrawer = true
                )
                printTask.postValue(PrintTask(printData, true))
            } ?: kotlin.run {
                Timber.i("Sales Saving process finish all... Grand Total : ${sales.grandTotal}")
                if (!isOpeningCashDrawer) pDialogTaskCommand.postValue(false)
                paymentSavedTask.postValue(sales)
            }
        }
    }

    private fun isValidPhoneNumber(phone: String): Boolean {
//        if(!phone.startsWith("08") && !phone.startsWith("62")) {
//            Timber.i("$phone phone not valid")
//            return false
//        }
        if (phone.length < 10 || phone.length > 15) {
            Timber.i("$phone phone not valid")
            return false
        }
        return true
    }

    fun printBill(employee: Employee, outlet: Outlet) {
        viewModelScope.launch {
            val printers = settingRepository.getPrinterList()
            val printData = ArrayList<PendingPrintEntity>()
            printers.filter { it.settingPrintreceipt == "1" }?.forEach { printerReceipt ->
                val nota = PrintNotaUtil.getPrintNotaFormat(
                    sales, outlet, employee, true, printerReceipt.settingPrintpapersize
                )
                printData.add(
                    PendingPrintEntity(
                        printerReceipt.address,
                        printerReceipt.type,
                        nota.first,
                        printerReceipt.name,
                        System.currentTimeMillis()
                    )
                )
            }
            printTask.postValue(PrintTask(printData))
        }
    }

    fun generatePaymentOptions(grandTotal: Int): ArrayList<Int> {
        val options = ArrayList<Int>()
        options.add(grandTotal)
        piutang?.let {
            listOf(0.75, 0.50, 0.25).forEach { divider ->
                val option = grandTotal * divider
                if (option <= 0) {
                    return@forEach
                }
                if (option % 10000 > 0) {
                    val newOption =
                        option - (option % 10000) + if (option % 10000 < 5000) 5000 else 10000
                    if (!options.contains(newOption.toInt())) options.add(newOption.toInt())
                }
                options.add(option.toInt())
            }
        } ?: kotlin.run {
            if (grandTotal % 10000 > 5000) {
                options.add(grandTotal - (grandTotal % 10000) + 10000)
            } else if (grandTotal % 5000 < 5000) {
                options.add(grandTotal - (grandTotal % 5000) + 5000)
            }
            listOf(10000, 20000, 50000, 100000).forEach {
                if (it > (options.last() % 100000)) {
                    options.add(it + (options.last() - (options.last() % 100000)))
                }
            }
        }
        Timber.i("[PAYMENT OPTION] grandTotal : $grandTotal | options : $options | piutang ? ${piutang != null}")
        return options
    }

    fun loadReceiptReceiver() {
        viewModelScope.launch {
            receiptReceiverList.clear()
            receiptReceiverList.addAll(salesRepository.getReceiptReceivers())
            receiptListRefresh.call()
            Timber.i("receipt receiver size : ${receiptReceiverList.size} | $receiptReceiverList")
        }
    }

    fun createInstantPayment() {
        if (paymentListSelected.any { it.total > 0 }) {
            _toastMessage.postValue(Event(ToastMessage("instant payment belum mendukung multi payment")))
            return
        }
        if (salesOriginal != null) {
            _toastMessage.postValue(Event(ToastMessage("instant payment belum mendukung split bill")))
            return
        }
        if (isPiutang) {
            _toastMessage.postValue(Event(ToastMessage("instant payment belum mendukung pembayaran piutang")))
            return
        }
        viewModelScope.launch {
            pDialogTaskCommand.postValue(true)

            //if not cart, save it first
            val cart = await { salesRepository.getSalesCart(sales.noNota) }
            if (cart == null) {
                val outlet =
                    sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java) ?: Outlet()
                salesRepository.saveTmpSales(
                    TmpSalesEntity(
                        sales.noNota,
                        Gson().toJson(sales),
                        outlet.outletId.safe(),
                        status = Constant.SALES_STATUS_TEMPORARY
                    ), true
                )
            }

            val payment = salesRepository.createInstantPayment(sales.noNota).await()
            pDialogTaskCommand.postValue(false)
            payment.data?.takeIf { payment.status }?.let { result ->
                navigateToPaymentQr.postValue(result)
            } ?: run {
                _toastMessage.postValue(Event(ToastMessage(payment.message, Level.ERROR)))
            }
        }
    }

    fun checkPaymentStatus(salesId: String?) {
        if (salesId == null) {
            return
        }

        viewModelScope.launch {
            val result = salesRepository.checkPaymentStatus(salesId.safe()).await()
            result.takeIf { it.status }?.data?.let { status ->
                if (status.status == "paid") {
                    _toastMessage.postValue(Event(ToastMessage("payment succeeded")))
                    taskPaymentPaid.call()
                }
            }
        }
    }

    fun saveSalesFromPayment(isSilent: Boolean = false) {
        viewModelScope.launch {
            pDialogTaskCommand.postValue(!isSilent)
            val result = salesRepository.checkPaymentStatus(sales.noNota).await()
            Timber.i("payment status of '${sales.noNota}' : ${Gson().toJson(result)}")
            val status = result.takeIf { it.status }?.data?.status ?: "paid"
            pDialogTaskCommand.postValue(false)

            if (status == "paid") {
                val salesCreated = salesRepository.getSalesByIds(sales.noNota).await()
                Timber.i("fetch sales: ${sales.noNota}, ${salesCreated.status} -> ${salesCreated.data?.size} ")
                salesCreated.takeIf { it.status.safe() }?.data?.firstOrNull()?.let {
                    sales.displayNota = it.displayNota.safe()
                }
                sales.payment = "CARD"
                sales.payments =
                    arrayListOf(Payment("CARD", getGrandTotal(), pay = getGrandTotal()))
                paymentSavedTask.postValue(sales)
            } else {
                if (!isSilent) _toastMessage.postValue(Event(ToastMessage("failed, payment status: $status")))
            }
        }
    }

    fun preparePrintQr(url: String?, total: Int, bitmap: Bitmap, timeEnd: Long) {
        viewModelScope.launch {
            pDialogTaskCommand.postValue(true)
            val printers = settingRepository.getPrinterList()
            val offPrinters = sharedPref.deactivatePrinterList()
            val printerReceipts = printers.filter { p -> p.settingPrintreceipt == "1" }
            Timber.i("deactivate printer: $offPrinters | printer receipts: ${printerReceipts.map { it.address }}")
            printerReceipts.firstOrNull { p -> !offPrinters.any { addrs -> addrs == p.address } }
                ?.let { printer ->
                    val dateFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
                    val formattedEndTime = dateFormat.format(Date(timeEnd))

                    val printText = StringBuilder()
                    printText.appendLine("${Constant.PRINTER_CODE_QR} $url ${Constant.PRINTER_CODE_QR}")
                    printText.appendLine("")
                    printText.appendLine(total.toCurrency("Rp"))
                    printText.appendLine("Lakukan Pembayaran Sebelum:")
                    printText.appendLine(formattedEndTime)

                    val printData = PendingPrintEntity(
                        printer.address, printer.type, dataString = printText.toString()
                    )
//                printData.bitmap = bitmap
                    printTask.postValue(PrintTask(listOf(printData), false))
                } ?: run {
                _toastMessage.postValue(Event(ToastMessage("no printer...")))
            }
            pDialogTaskCommand.postValue(false)
        }
    }

    fun selectBank(position: Int) {
        selectedPayment.selectedBankIdx = position
        selectedPayment.description = bankList[position].name.safe()
        if (selectedPayment.total == 0) {
            val change = calculateChange()
            if (change < 0) {
                selectedPayment.total = abs(change)
//                paymentValue[selectedPaymentIdx] =  abs(change)
                countChange()
            }
        }
    }

    fun calculateChange(): Int = paymentListSelected.sumOf { it.total } - getGrandTotal()
}