package com.uniq.uniqpos.view.piutang


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import com.uniq.uniqpos.R
import com.uniq.uniqpos.databinding.FragmentPiutangListBinding
import com.uniq.uniqpos.databinding.ListItemPiutangBinding
import com.uniq.uniqpos.util.view.RecyclerItemClickListener
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import androidx.core.view.isVisible
import timber.log.Timber


/**
 * A simple [Fragment] subclass.
 *
 */
class PiutangListFragment : Fragment() {

    private var _binding: FragmentPiutangListBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: PiutangViewModel
    private var selectedItem = -1

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPiutangListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel = (activity as PiutangActivity).viewModel
        binding.recviewPiutang.adapter = object : GlobalAdapter<ListItemPiutangBinding>(
            R.layout.list_item_piutang,
            viewModel.piutangListShow,
            binding.incLayNoData.layoutNoData
        ) {
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemPiutangBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                if ((activity as PiutangActivity).isUseTabLayout) {
                    val background = if (position == selectedItem) ContextCompat.getColor(
                        context!!,
                        R.color.background_selected
                    ) else 0
                    holder.binding.mainPiutang.setBackgroundColor(background)
                }
            }
        }

        binding.recviewPiutang.addOnItemTouchListener(RecyclerItemClickListener(context) { _, position ->
            val tmpSelected = selectedItem
            selectedItem = position
            binding.recviewPiutang.adapter?.notifyItemChanged(tmpSelected)
            binding.recviewPiutang.adapter?.notifyItemChanged(position)
            (activity as PiutangActivity).onItemSelected(position)
        })
        observeData()
    }

    private fun observeData() {
        viewModel.refreshPiutang.observe(viewLifecycleOwner) {
            if (binding.progressBar.isVisible) binding.progressBar.visibility =
                View.GONE

            if (viewModel.isFirstLoad && viewModel.piutangListShow.isNotEmpty() && viewModel.piutangListShow[0].sales != null && (activity as PiutangActivity).isUseTabLayout) {
                (activity as PiutangActivity).onItemSelected(0)
                selectedItem = 0
                viewModel.isFirstLoad = false
            }
            Timber.i("refresh piutang list, size of data ${viewModel.piutangListShow.size}")
            binding.recviewPiutang.adapter?.notifyDataSetChanged()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
