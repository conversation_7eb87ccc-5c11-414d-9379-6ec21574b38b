package com.uniq.uniqpos.view.piutang

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.PiutangEntity
import com.uniq.uniqpos.data.local.entity.PiutangHistoryEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.remote.repository.SalesRepository
import com.uniq.uniqpos.util.lifecycle.SingleLiveAction
import com.uniq.uniqpos.util.lifecycle.SingleLiveEvent
import com.uniq.uniqpos.util.safe
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * Created by annasblackhat on 01/08/18
 */

class PiutangViewModel @Inject constructor(val salesRepository: SalesRepository) : ViewModel() {

    val piutangList = ArrayList<PiutangEntity>()
    val piutangListShow = ArrayList<PiutangEntity>() //which used to show to ui
    val piutangHistoryList = ArrayList<PiutangHistoryEntity>()
    val salesList = ArrayList<SalesEntity>()
    var isFirstLoad = true

    val refreshPiutang = SingleLiveAction()

//    fun getPiutangLive(outletId: Int?) = salesRepository.getPiutangLive(outletId)
    fun getPiutangLive(outletId: Int) = salesRepository.getPiutangLiveDb(outletId)

    fun getPiutangHistoryByIdLive(piutangId: Long) =
        salesRepository.getPiutangHistoryById(piutangId)
    fun getSalesByIdLive(id: String) = salesRepository.getSalesByIdLive(id)

    fun findSales() {
        val salesIds =
            piutangList.map { it.salesFkid }.filter { id -> !salesList.any { it.noNota == id } }
        Timber.i("saved sales total : ${salesList.size} | new sales ids total : ${salesIds.size}")
        viewModelScope.launch {
            try {
                val salesByIds = salesRepository.getSalesByIds(salesIds)
//                salesList.clear()
                salesList.addAll(salesByIds)
                piutangList.forEach { piutang ->
                    salesList.firstOrNull { it.noNota == piutang.salesFkid }
                        ?.let { sales ->
                            var isRefund = salesByIds.any {
                                it.noNota == sales.noNota && it.status.safe()
                                    .lowercase() == "refund"
                            }

                            Timber.d("${sales.noNota} is refund: $isRefund")
                            if (!isRefund) {
                                sales.salesTag = null
                                piutang.sales = sales
                            }
                        }
                        ?: run { Timber.i("not found for sales id ${piutang.salesFkid}") }
                }
                Timber.d("piutang : ${Gson().toJson(piutangList)}")
                Timber.i("all piutang size : ${piutangList.size}, piutang with sales size: ${piutangList.filter { it.sales != null }.size}, salesByIds size: ${salesByIds.size}")
                piutangListShow.clear()
                piutangListShow.addAll(piutangList.filter { it.sales != null })
                refreshPiutang.call()
            } catch (e: Exception) {
                Timber.i("[ERROR] finding sales err : $e")
            }
        }
    }

    fun searchData(q: String?) {
        piutangListShow.clear()
        q?.let { query ->
            piutangListShow.addAll(piutangList.filter {
                it.sales?.customer?.contains(query).safe() || it.sales?.displayNota?.contains(query)
                    .safe()
            })
        } ?: run { piutangListShow.addAll(piutangList) }

        isFirstLoad = true //set this, in order to select first item
        refreshPiutang.call()
    }
}